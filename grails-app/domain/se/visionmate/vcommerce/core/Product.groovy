package se.visionmate.vcommerce.core

import se.visionmate.vcommerce.enums.Language
import se.visionmate.vcommerce.enums.ProductStatus
import se.visionmate.vcommerce.enums.ProductType

class Product {
    String number
    String description
    String htmlDescription
    String productClass
    String itemClassId
    Integer availability
    Integer groupNumber
    Long wooId
    Long vismaId
    Boolean visible
    ProductStatus status
    ProductType type
    Date dateCreated
    Date lastUpdated

    static hasOne = [priceClass: PriceClass]

    static hasMany = [
            prices: Price,
            categories: Category,
            attributes: Attribute,
            attributeTerms: AttributeTerm,
            attachments: Attachment,
            groupedProducts: Product,
            tags: Tag,
            discounts: Discount,
            texts: ProductText
    ]

    static constraints = {
        number unique: true, dispaly: true
        description blank: true, nullable: true, display: true
        htmlDescription blank: true, nullable: true, dispaly: true, widget: 'textarea'
        productClass blank: true, nullable: true, display: false
        itemClassId blank: true, nullable: true, display: false
        availability display:true
        groupNumber nullable: true, display: true
        wooId unique: true, nullable: true, dispaly: true
        vismaId unique: true, display: true
        visible nullable: true
        prices display: true
        priceClass nullable: true
        categories display: true
        attributes display: true
        attributeTerms display: true
        attachments dispaly: false
    }

    static mapping = {
        dateCreated column: 'created'
        lastUpdated column: 'updated'
        htmlDescription type: 'text'
    }

    @Override
    String toString() {
        "$number $description"
    }

    String getCleanHtmlDescription() {
        this.htmlDescription.replaceAll(/<style>([\s\S]*)<\/style>/, '')
    }

    String getDescription(Language lang) {
        texts.find { it.lang == lang }?.description
    }

    String getHtmlDescription(Language lang) {
        texts.find { it.lang == lang }?.htmlDescription
    }

    void setDescription(Language lang, String description) {
        ProductText text = texts?.find { it.lang == lang }
        if (text) {
            text.description = description
        } else {
            addToTexts(new ProductText(lang: lang, description: description, product: this))
//            texts.add(new ProductText(lang: lang, description: description, product: this))
        }
    }

    void setHtmlDescription(Language lang, String htmlDescription) {
        ProductText text = texts?.find { it.lang == lang }
          if (text) {
            text.htmlDescription = htmlDescription
        } else {
            addToTexts(new ProductText(lang: lang, htmlDescription: htmlDescription, product: this))
//            texts.add(new ProductText(lang: lang, htmlDescription: htmlDescription, product: this))
        }
    }

}

class ProductText {
    Language lang
    String description
    String htmlDescription
    int wooId

    static belongsTo = [product: Product]

    static constraints = {
        lang(nullable: false, blank: false)
        description(nullable: true, blank: true)
        htmlDescription(nullable: true, blank: true)
    }

    @Override
    String toString() {
        "$product.number $description ($lang)"
    }
}
